{"name": "@onflow/six-withdraw-unlocked-flow", "version": "0.3.1", "description": "Flow SDK Stored Interaction - Withdraw Unlocked Flow", "license": "Apache-2.0", "author": "Dapper Labs <<EMAIL>>", "homepage": "https://onflow.org", "repository": {"type": "git", "url": "git+ssh://**************/onflow/fcl-six.git"}, "bugs": {"url": "https://github.com/onflow/fcl-six/issues"}, "jest": {"transform": {".js": "jest-esm-transformer"}}, "devDependencies": {"jest": "25.3.0", "jest-esm-transformer": "1.0.0", "microbundle": "0.12.0-next.8"}, "source": "src/six-withdraw-unlocked-flow.js", "main": "dist/six-withdraw-unlocked-flow.js", "module": "dist/six-withdraw-unlocked-flow.module.js", "unpkg": "dist/six-withdraw-unlocked-flow.umd.js", "scripts": {"prepublishOnly": "npm test && npm run build", "test": "jest", "build": "microbundle", "test:watch": "jest --watch", "start": "microbundle watch"}, "peerDependencies": {"@onflow/config": ">=1.1.0", "@onflow/fcl": ">=1.4.0 || >=1.5.0 || >=1.6.0 || >=1.7.0", "@onflow/types": ">=1.1.0"}, "publishConfig": {"access": "public"}}