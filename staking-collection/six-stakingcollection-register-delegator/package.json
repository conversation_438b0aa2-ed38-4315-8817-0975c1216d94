{"name": "@onflow/six-stakingcollection-register-delegator", "version": "0.2.2", "description": "Flow SDK Stored Interaction - Registers a Delegator held in Staking Collection", "license": "Apache-2.0", "author": "Dapper Labs <<EMAIL>>", "homepage": "https://onflow.org", "repository": {"type": "git", "url": "git+ssh://**************/onflow/fcl-six.git"}, "bugs": {"url": "https://github.com/onflow/fcl-six/issues"}, "jest": {"transform": {".js": "jest-esm-transformer"}}, "devDependencies": {"jest": "25.3.0", "jest-esm-transformer": "1.0.0", "microbundle": "0.12.0-next.8"}, "source": "src/six-stakingcollection-register-delegator.js", "main": "dist/six-stakingcollection-register-delegator.js", "module": "dist/six-stakingcollection-register-delegator.module.js", "unpkg": "dist/six-stakingcollection-register-delegator.umd.js", "scripts": {"prepublishOnly": "npm test && npm run build", "test": "jest", "build": "microbundle", "test:watch": "jest --watch", "start": "microbundle watch"}, "peerDependencies": {"@onflow/fcl": ">=1.4.0 || >=1.5.0 || >=1.6.0 || >=1.7.0"}, "publishConfig": {"access": "public"}}