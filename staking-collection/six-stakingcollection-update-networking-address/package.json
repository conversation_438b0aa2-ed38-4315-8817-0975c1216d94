{"name": "@onflow/six-stakingcollection-update-networking-address", "version": "0.2.2", "description": "Flow SDK Stored Interaction - Updates a networking address for a node held in a Staking Collection", "license": "Apache-2.0", "author": "Dapper Labs <<EMAIL>>", "homepage": "https://onflow.org", "repository": {"type": "git", "url": "git+ssh://**************/onflow/fcl-six.git"}, "bugs": {"url": "https://github.com/onflow/fcl-six/issues"}, "jest": {"transform": {".js": "jest-esm-transformer"}}, "devDependencies": {"jest": "25.3.0", "jest-esm-transformer": "1.0.0", "microbundle": "0.12.0-next.8"}, "source": "src/six-stakingcollection-update-networking-address.js", "main": "dist/six-stakingcollection-update-networking-address.js", "module": "dist/six-stakingcollection-update-networking-address.module.js", "unpkg": "dist/six-stakingcollection-update-networking-address.umd.js", "scripts": {"prepublishOnly": "npm test && npm run build", "test": "jest", "build": "microbundle", "test:watch": "jest --watch", "start": "microbundle watch"}, "peerDependencies": {"@onflow/fcl": ">=1.4.0 || >=1.5.0 || >=1.6.0 || >=1.7.0"}, "publishConfig": {"access": "public"}}