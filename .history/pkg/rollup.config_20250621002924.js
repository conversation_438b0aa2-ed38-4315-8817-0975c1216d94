import swc from '@rollup/plugin-swc';
import terser from '@rollup/plugin-terser';
import commonjs from '@rollup/plugin-commonjs';
import { visualizer } from "rollup-plugin-visualizer";
import nodeExternals from 'rollup-plugin-node-externals';
import { nodeResolve } from '@rollup/plugin-node-resolve';

function removeBlank() {
  return {
    name: 'remove-blank-lines',
    generateBundle(_, bundle) {
      for (const file of Object.values(bundle)) {
        if (file.type === 'chunk') {
          file.code = file.code.replace(/\n\s*\n/g, '\n');
        }
      }
    }
  };
}

export default {
  input: 'entry.js',
  output: {
    file: 'bundle-fcl-six-address.js',
    format: 'esm',
    sourcemap: true
  },

  external: (id) => {
    return id.includes('node_modules') || id.startsWith('@walletconnect') || id.startsWith('viem') || id.startsWith('ox') || id.startsWith('pino') || id.startsWith('detect-browser') || ['cross-fetch', 'isomorphic-ws', 'sha3', '@improbable-eng/grpc-web', 'google-protobuf'].includes(id);
  },

  plugins: [
    nodeExternals(),

    nodeResolve({
      preferBuiltins: false,
      mainFields: ['module'], // Default: ['module', 'main']
    }),

    commonjs({
      transformMixedEsModules: true,
      include: [/.*_pb\.js$/, /google-protobuf/],
      // 确保proto对象可用
      requireReturnsDefault: 'preferred',
      esmExternals: true
    }),

    swc({
      "tsconfig": false,
      "jsc": {
        "parser": {
          "syntax": "ecmascript",
          "jsx": false
        },
        "target": "es2022",
        "loose": true,
        "minify": false
      },
      "module": {
        "type": "es6"
      },
      "minify": false,
      "isModule": true
    }),

    terser({
      "compress": {
        "collapse_vars": true,
        "hoist_vars": true,
      },
      "mangle": false,
      "format": {
        comments: false,
        beautify: true,
        indent_level: 2,
      }
    }),

    removeBlank(),

    visualizer(),
  ],

  treeshake: {
    moduleSideEffects: false,
    propertyReadSideEffects: false,
    tryCatchDeoptimization: false,
    unknownGlobalSideEffects: false,
    correctVarValueBeforeDeclaration: false,
    annotations: true,
    preset: 'smallest'
  }
};