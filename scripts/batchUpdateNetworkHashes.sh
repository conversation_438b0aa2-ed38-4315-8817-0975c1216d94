#!/bin/bash

npm run batchUpdateNetworkHashes "Create Account" six-create-account/src/six-create-account.js
npm run batchUpdateNetworkHashes "Add Key" six-add-new-key/src/six-add-new-key.js
npm run batchUpdateNetworkHashes "Remove Key" six-remove-key/src/six-remove-key.js
npm run batchUpdateNetworkHashes "Setup Fungible Token Vault" six-setup-token-vault/src/six-setup-token-vault.js 
npm run batchUpdateNetworkHashes "Transfer Fungible Token with Paths" six-transfer-tokens-with-paths/src/six-transfer-tokens-with-paths.js 
npm run batchUpdateNetworkHashes "Transfer Fungible Token with Address" six-transfer-tokens-with-address/src/six-transfer-tokens-with-address.js 
npm run batchUpdateNetworkHashes "Setup NFT Collection" six-setup-nft-collection/src/six-setup-nft-collection.js
npm run batchUpdateNetworkHashes "Transfer NFT with Paths" six-transfer-nft-with-paths/src/six-transfer-nft-with-paths.js
npm run batchUpdateNetworkHashes "Transfer NFT with Address" six-transfer-nft-with-address/src/six-transfer-nft-with-address.js
npm run batchUpdateNetworkHashes "Withdraw Unlocked FLOW" six-withdraw-unlocked-flow/src/six-withdraw-unlocked-flow.js
npm run batchUpdateNetworkHashes "Deposit Unlocked FLOW" six-deposit-unlocked-flow/src/six-deposit-unlocked-flow.js
npm run batchUpdateNetworkHashes "Setup Staking Collection" staking-collection/six-stakingcollection-setup-staking-collection/src/six-stakingcollection-setup-staking-collection.js
npm run batchUpdateNetworkHashes "Register Delegator" staking-collection/six-stakingcollection-register-delegator/src/six-stakingcollection-register-delegator.js
npm run batchUpdateNetworkHashes "Register Node" staking-collection/six-stakingcollection-register-node/src/six-stakingcollection-register-node.js SCO.03
npm run batchUpdateNetworkHashes "Register Node with PoP" staking-collection/six-stakingcollection-register-node-with-pop/src/six-stakingcollection-register-node-with-pop.js SCO.17
npm run batchUpdateNetworkHashes "Create Machine Account" staking-collection/six-stakingcollection-create-machine-account/src/six-stakingcollection-create-machine-account.js
npm run batchUpdateNetworkHashes "Request Unstaking" staking-collection/six-stakingcollection-request-unstaking/src/six-stakingcollection-request-unstaking.js
npm run batchUpdateNetworkHashes "Stake New Tokens" staking-collection/six-stakingcollection-stake-new-tokens/src/six-stakingcollection-stake-new-tokens.js
npm run batchUpdateNetworkHashes "Stake Rewarded Tokens" staking-collection/six-stakingcollection-stake-rewarded-tokens/src/six-stakingcollection-stake-rewarded-tokens.js
npm run batchUpdateNetworkHashes "Stake Unstaked Tokens" staking-collection/six-stakingcollection-stake-unstaked-tokens/src/six-stakingcollection-stake-unstaked-tokens.js
npm run batchUpdateNetworkHashes "Unstake All" staking-collection/six-stakingcollection-unstake-all/src/six-stakingcollection-unstake-all.js
npm run batchUpdateNetworkHashes "Withdraw Rewarded Tokens" staking-collection/six-stakingcollection-withdraw-rewarded-tokens/src/six-stakingcollection-withdraw-rewarded-tokens.js
npm run batchUpdateNetworkHashes "Withdraw Unstaked Tokens" staking-collection/six-stakingcollection-withdraw-unstaked-tokens/src/six-stakingcollection-withdraw-unstaked-tokens.js
npm run batchUpdateNetworkHashes "Close Stake" staking-collection/six-stakingcollection-close-stake/src/six-stakingcollection-close-stake.js
npm run batchUpdateNetworkHashes "Transfer Node" staking-collection/six-stakingcollection-transfer-node/src/six-stakingcollection-transfer-node.js
npm run batchUpdateNetworkHashes "Transfer Delegator" staking-collection/six-stakingcollection-transfer-delegator/src/six-stakingcollection-transfer-delegator.js
npm run batchUpdateNetworkHashes "Withdraw From Machine Account" staking-collection/six-stakingcollection-withdraw-from-machine-account/src/six-stakingcollection-withdraw-from-machine-account.js
npm run batchUpdateNetworkHashes "Update Networking Address" staking-collection/six-stakingcollection-update-networking-address/src/six-stakingcollection-update-networking-address.js