import * as fcl from "@onflow/fcl";
import { pipe, invariant, transaction, args, proposer, authorizations, payer, arg } from "@onflow/fcl";
import * as t from "@onflow/types";
import { String, UInt8, UFix64 } from "@onflow/types";
import { config } from "@onflow/config";
const template$1 = ({proposer: proposer$1, authorization: authorization, payer: payer$1, publicKey: publicKey, signatureAlgorithm: signatureAlgorithm, hashAlgorithm: hashAlgorithm, weight: weight}) => pipe([ invariant("" !== publicKey, "template({publicKey}) -- publicKey must not be an empty string."), invariant(null !== signatureAlgorithm, "template({signatureAlgorithm}) -- signatureAlgorithm must not be null."), invariant(null !== hashAlgorithm, "template({hashAlgorithm}) -- hashAlgorithm must not be null."), invariant("" !== weight, "template({weight}) -- weight must not be an empty string."), transaction('import Crypto\n\ntransaction(key: String, signatureAlgorithm: UInt8, hashAlgorithm: UInt8, weight: UFix64) {\n\tprepare(signer: auth(BorrowValue, Storage) &Account) {\n\t\tpre {\n\t\t\tsignatureAlgorithm >= 1 && signatureAlgorithm <= 3: "Must provide a signature algorithm raw value that is 1, 2, or 3"\n\t\t\thashAlgorithm >= 1 && hashAlgorithm <= 6: "Must provide a hash algorithm raw value that is between 1 and 6"\n\t\t\tweight <= 1000.0: "The key weight must be between 0 and 1000"\n\t\t}\n\n\t\tlet publicKey = PublicKey(\n\t\t\tpublicKey: key.decodeHex(),\n\t\t\tsignatureAlgorithm: SignatureAlgorithm(rawValue: signatureAlgorithm)!\n\t\t)\n\n\t\tlet account = Account(payer: signer)\n\n\t\taccount.keys.add(publicKey: publicKey, hashAlgorithm: HashAlgorithm(rawValue: hashAlgorithm)!, weight: weight)\n\t}\n}'), args([ arg(publicKey, String), arg(signatureAlgorithm, UInt8), arg(hashAlgorithm, UInt8), arg(weight, UFix64) ]), proposer(proposer$1), authorizations([ authorization ]), payer(payer$1) ]), DEPS = new Set([ "0xFUNGIBLETOKENADDRESS", "0xFUNGIBLETOKENMETADATAVIEWS" ]);
class UndefinedConfigurationError extends Error {
  constructor(address) {
    super(`Stored Interaction Error: Missing configuration for ${address}. Please see the following to learn more: https://github.com/onflow/fcl-six/blob/main/six-transfer-tokens/README.md`.trim()), 
    this.name = "Stored Interaction Undefined Address Configuration Error";
  }
}
const addressCheck = async address => {
  if (!await config().get(address)) throw new UndefinedConfigurationError(address);
}, template = async ({proposer: proposer, authorization: authorization, payer: payer, amount: amount = "", to: to = "", contractAddress: contractAddress = "", contractName: contractName = ""}) => {
  for (let addr of DEPS) await addressCheck(addr);
  return fcl.pipe([ fcl.transaction('import FungibleToken from 0xFUNGIBLETOKENADDRESS\nimport FungibleTokenMetadataViews from 0xFUNGIBLETOKENMETADATAVIEWS\n\n/// Can pass in any contract address and name to transfer a token from that contract\n/// This lets you choose the token you want to send\n///\n/// Any contract can be chosen here, so wallets should check argument values\n/// to make sure the intended token contract name and address is passed in\n///\ntransaction(amount: UFix64, to: Address, contractAddress: Address, contractName: String) {\n\n    // The Vault resource that holds the tokens that are being transferred\n    let tempVault: @{FungibleToken.Vault}\n\n    // FTVaultData struct to get paths from\n    let vaultData: FungibleTokenMetadataViews.FTVaultData\n\n    prepare(signer: auth(BorrowValue) &Account) {\n\n        // Borrow a reference to the vault stored on the passed account at the passed publicPath\n        let resolverRef = getAccount(contractAddress)\n            .contracts.borrow<&{FungibleToken}>(name: contractName)\n            ?? panic("Could not borrow a reference to the fungible token contract")\n\n        // Use that reference to retrieve the FTView\n        self.vaultData = resolverRef.resolveContractView(resourceType: nil, viewType: Type<FungibleTokenMetadataViews.FTVaultData>()) as! FungibleTokenMetadataViews.FTVaultData?\n            ?? panic("Could not resolve the FTVaultData view for the given Fungible token contract")\n\n        // Get a reference to the signer\'s stored vault\n        let vaultRef = signer.storage.borrow<auth(FungibleToken.Withdraw) &{FungibleToken.Provider}>(from: self.vaultData.storagePath)\n\t\t\t?? panic("Could not borrow reference to the owner\'s Vault!")\n\n        self.tempVault <- vaultRef.withdraw(amount: amount)\n    }\n\n    execute {\n        let recipient = getAccount(to)\n        let receiverRef = recipient.capabilities.borrow<&{FungibleToken.Receiver}>(self.vaultData.receiverPath)\n            ?? panic("Could not borrow reference to the recipient\'s Receiver!")\n\n        // Transfer tokens from the signer\'s stored vault to the receiver capability\n        receiverRef.deposit(from: <-self.tempVault)\n    }\n}'), fcl.args([ fcl.arg(amount, t.UFix64), fcl.arg(to, t.Address), fcl.arg(contractAddress, t.Address), fcl.arg(contractName, t.String) ]), fcl.proposer(proposer), fcl.authorizations([ authorization ]), fcl.payer(payer) ]);
};
export { template$1 as createAccount, template as transferTokens };
//# sourceMappingURL=bundle-fcl-six.js.map
