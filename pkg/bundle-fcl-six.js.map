{"version": 3, "file": "bundle-fcl-six.js", "sources": ["../six-create-account/src/six-create-account.js", "../six-transfer-tokens-with-address/src/six-transfer-tokens-with-address.js"], "sourcesContent": ["import { pipe, invariant, transaction, args, arg, proposer as fclProposer, authorizations, payer as fclPayer } from \"@onflow/fcl\"\nimport { String, UInt8, UFix64 } from \"@onflow/types\"\n\nexport const TITLE = \"Create Account\"\nexport const DESCRIPTION = \"Create an Account on Flow with a Public Key.\"\nexport const VERSION = \"0.2.1\"\nexport const HASH = \"63d8b6a045bf8e6196198184db685c2cf22932503ccb2dcb85c7d2dc04c882ba\"\nexport const CODE = `import Crypto\n\ntransaction(key: String, signatureAlgorithm: UInt8, hashAlgorithm: UInt8, weight: UFix64) {\n\tprepare(signer: auth(BorrowValue, Storage) &Account) {\n\t\tpre {\n\t\t\tsignatureAlgorithm >= 1 && signatureAlgorithm <= 3: \"Must provide a signature algorithm raw value that is 1, 2, or 3\"\n\t\t\thashAlgorithm >= 1 && hashAlgorithm <= 6: \"Must provide a hash algorithm raw value that is between 1 and 6\"\n\t\t\tweight <= 1000.0: \"The key weight must be between 0 and 1000\"\n\t\t}\n\n\t\tlet publicKey = PublicKey(\n\t\t\tpublicKey: key.decodeHex(),\n\t\t\tsignatureAlgorithm: SignatureAlgorithm(rawValue: signatureAlgorithm)!\n\t\t)\n\n\t\tlet account = Account(payer: signer)\n\n\t\taccount.keys.add(publicKey: publicKey, hashAlgorithm: HashAlgorithm(rawValue: hashAlgorithm)!, weight: weight)\n\t}\n}`\n\nexport const template = ({\n    proposer,\n    authorization,\n    payer,\n    publicKey,\n    signatureAlgorithm,\n    hashAlgorithm,\n    weight\n}) => pipe([\n    invariant(publicKey !== \"\", \"template({publicKey}) -- publicKey must not be an empty string.\"),\n    invariant(signatureAlgorithm !== null, \"template({signatureAlgorithm}) -- signatureAlgorithm must not be null.\"),\n    invariant(hashAlgorithm !== null, \"template({hashAlgorithm}) -- hashAlgorithm must not be null.\"),\n    invariant(weight !== \"\", \"template({weight}) -- weight must not be an empty string.\"),\n    transaction(CODE),\n    args([\n        arg(publicKey, String),\n        arg(signatureAlgorithm, UInt8),\n        arg(hashAlgorithm, UInt8),\n        arg(weight, UFix64)\n    ]),\n    fclProposer(proposer),\n    authorizations([authorization]),\n    fclPayer(payer)\n])\n\nexport const MAINNET_HASH = `c4a7efd8708396e8c7a3611f72a9f89f675bf6d5c9336dd389e5839cba78443c`\nexport const TESTNET_HASH = `c4a7efd8708396e8c7a3611f72a9f89f675bf6d5c9336dd389e5839cba78443c`", "import * as fcl from \"@onflow/fcl\"\nimport * as t from \"@onflow/types\"\nimport {config} from \"@onflow/config\"\n\nconst DEPS = new Set([\n    \"0xFUNGIBLETOKENADDRESS\",\n    \"0xFUNGIBLETOKENMETADATAVIEWS\"\n])\n\nexport const TITLE = \"Transfer Tokens with addresses\"\nexport const DESCRIPTION = \"Transfer tokens from one account to another.\"\nexport const VERSION = \"0.2.3\"\nexport const HASH = \"47851586d962335e3f7d9e5d11a4c527ee4b5fd1c3895e3ce1b9c2821f60b166\"\nexport const CODE = `import FungibleToken from 0xFUNGIBLETOKENADDRESS\nimport FungibleTokenMetadataViews from 0xFUNGIBLETOKENMETADATAVIEWS\n\n/// Can pass in any contract address and name to transfer a token from that contract\n/// This lets you choose the token you want to send\n///\n/// Any contract can be chosen here, so wallets should check argument values\n/// to make sure the intended token contract name and address is passed in\n///\ntransaction(amount: UFix64, to: Address, contractAddress: Address, contractName: String) {\n\n    // The Vault resource that holds the tokens that are being transferred\n    let tempVault: @{FungibleToken.Vault}\n\n    // FTVaultData struct to get paths from\n    let vaultData: FungibleTokenMetadataViews.FTVaultData\n\n    prepare(signer: auth(BorrowValue) &Account) {\n\n        // Borrow a reference to the vault stored on the passed account at the passed publicPath\n        let resolverRef = getAccount(contractAddress)\n            .contracts.borrow<&{FungibleToken}>(name: contractName)\n            ?? panic(\"Could not borrow a reference to the fungible token contract\")\n\n        // Use that reference to retrieve the FTView\n        self.vaultData = resolverRef.resolveContractView(resourceType: nil, viewType: Type<FungibleTokenMetadataViews.FTVaultData>()) as! FungibleTokenMetadataViews.FTVaultData?\n            ?? panic(\"Could not resolve the FTVaultData view for the given Fungible token contract\")\n\n        // Get a reference to the signer's stored vault\n        let vaultRef = signer.storage.borrow<auth(FungibleToken.Withdraw) &{FungibleToken.Provider}>(from: self.vaultData.storagePath)\n\t\t\t?? panic(\"Could not borrow reference to the owner's Vault!\")\n\n        self.tempVault <- vaultRef.withdraw(amount: amount)\n    }\n\n    execute {\n        let recipient = getAccount(to)\n        let receiverRef = recipient.capabilities.borrow<&{FungibleToken.Receiver}>(self.vaultData.receiverPath)\n            ?? panic(\"Could not borrow reference to the recipient's Receiver!\")\n\n        // Transfer tokens from the signer's stored vault to the receiver capability\n        receiverRef.deposit(from: <-self.tempVault)\n    }\n}`\n\nclass UndefinedConfigurationError extends Error {\n    constructor(address) {\n      const msg = `Stored Interaction Error: Missing configuration for ${address}. Please see the following to learn more: https://github.com/onflow/fcl-six/blob/main/six-transfer-tokens/README.md`.trim()\n      super(msg)\n      this.name = \"Stored Interaction Undefined Address Configuration Error\"\n    }\n}\n\nconst addressCheck = async address => {\n    if (!await config().get(address)) throw new UndefinedConfigurationError(address)\n}\n\nexport const template = async ({ proposer, authorization, payer, amount = \"\", to = \"\", contractAddress = \"\", contractName = \"\" }) => {\n    for (let addr of DEPS) await addressCheck(addr)\n\n    return fcl.pipe([\n        fcl.transaction(CODE),\n        fcl.args([fcl.arg(amount, t.UFix64), fcl.arg(to, t.Address), fcl.arg(contractAddress, t.Address), fcl.arg(contractName, t.String)]),\n        fcl.proposer(proposer),\n        fcl.authorizations([authorization]),\n        fcl.payer(payer)\n    ])\n}\n\nexport const MAINNET_HASH = `d8f826a451d808697ed3ce7908a080c05219df458e3e6cd4ccd073600c58e600`\nexport const TESTNET_HASH = `0adc1ebe8246cf7656aefd9bf336f7f0c102a039e343776da61da4d6aa39aed2`"], "names": ["template", "proposer", "authorization", "payer", "public<PERSON>ey", "signatureAlgorithm", "hashAlgorithm", "weight", "pipe", "invariant", "transaction", "args", "arg", "String", "UInt8", "UFix64", "fclProposer", "authorizations", "fcl<PERSON><PERSON><PERSON>", "DEPS", "Set", "UndefinedConfigurationError", "Error", "constructor", "address", "super", "trim", "this", "name", "addressCheck", "async", "config", "get", "amount", "to", "contractAddress", "contractName", "addr", "fcl", "t", "Address"], "mappings": ";;;;;;;;;;AAOO,MAqBMA,aAAW,EAACC,UACrBA,YACAC,qCACAC,SACAC,sBACAC,wCACAC,8BACAC,oBACEC,KAAK,EACPC,UAAwB,OAAdL,WAAkB,oEAC5BK,UAAiC,SAAvBJ,oBAA6B,2EACvCI,UAA4B,SAAlBH,eAAwB,iEAClCG,UAAqB,OAAXF,QAAe,8DACzBG,YAlCgB,qzBAmChBC,KAAK,EACDC,IAAIR,WAAWS,SACfD,IAAIP,oBAAoBS,QACxBF,IAAIN,eAAeQ,QACnBF,IAAIL,QAAQQ,YAEhBC,SAAYf,aACZgB,eAAe,EAACf,kBAChBgB,MAASf,aC9CPgB,OAAO,IAAIC,IAAI,EACjB,0BACA;;AAoDJ,MAAMC,oCAAoCC;EACtCC,WAAAA,CAAYC;IAEVC,MADY,uDAAuDD,6HAA6HE;IAEhMC,KAAKC,OAAO;AACd;;;AAGJ,MAAMC,eAAeC,MAAMN;EACvB,WAAWO,SAASC,IAAIR,UAAU,MAAM,IAAIH,4BAA4BG;GAG/DxB,WAAW8B,QAAS7B,oBAAUC,8BAAeC,cAAO8B,iBAAS,IAAIC,SAAK,IAAIC,mCAAkB,IAAIC,6BAAe;EACxH,KAAK,IAAIC,QAAQlB,YAAYU,aAAaQ;EAE1C,OAAOC,IAAI9B,KAAK,EACZ8B,IAAI5B,YA7DQ,woEA8DZ4B,IAAI3B,KAAK,EAAC2B,IAAI1B,IAAIqB,QAAQM,EAAExB,SAASuB,IAAI1B,IAAIsB,IAAIK,EAAEC,UAAUF,IAAI1B,IAAIuB,iBAAiBI,EAAEC,UAAUF,IAAI1B,IAAIwB,cAAcG,EAAE1B,YAC1HyB,IAAIrC,SAASA,WACbqC,IAAIrB,eAAe,EAACf,kBACpBoC,IAAInC,MAAMA;;;"}